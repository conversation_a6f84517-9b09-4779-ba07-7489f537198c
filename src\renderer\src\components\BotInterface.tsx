import { useState, useEffect } from 'react'

interface BotStatus {
  isRunning: boolean
  isPaused: boolean
  currentUrl: string
  status: string
}

const BotInterface: React.FC = () => {
  const [isStarted, setIsStarted] = useState(false)
  const [currentPosition, setCurrentPosition] = useState({ x: 0, y: 0 })
  const [screenDimensions, setScreenDimensions] = useState({ screenWidth: 0, screenHeight: 0 })

  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    isPaused: false,
    currentUrl: '',
    status: 'Ready'
  })

  useEffect(() => {
    // Listen for bot status updates from main process
    const removeBotStatus = window.api.onBotStatusUpdate?.((status: BotStatus) => {
      setBotStatus(status)
    })

    // Get initial window position when component mounts
    const getInitialPosition = async (): Promise<void> => {
      if (!window.api.getWindowPosition) return

      try {
        const result = await window.api.getWindowPosition?.()
        if (result?.success && result.position && result.screenDimensions) {
          setCurrentPosition(result.position)
          setScreenDimensions(result.screenDimensions)
        }
      } catch (error) {
        console.error('Error getting initial position:', error)
      }
    }

    getInitialPosition()

    return () => {
      removeBotStatus?.()
    }
  }, [])

  const handleBegin = async (): Promise<void> => {
    try {
      setIsStarted(true)

      // Small delay to allow UI transition to start
      await new Promise((resolve) => setTimeout(resolve, 100))

      // Position bot interface at right side and resize bot browser
      const interfaceWidth = 300
      const interfaceHeight = 600

      setBotStatus((prev) => ({ ...prev, status: 'Positioning interface...' }))

      const positionResult = await window.api.positionBotInterface?.(
        interfaceWidth,
        interfaceHeight
      )
      if (positionResult?.success) {
        console.log('Interface positioned successfully:', positionResult)
        setBotStatus((prev) => ({ ...prev, status: 'Interface positioned' }))

        // Update current position and screen dimensions
        if (positionResult.interfacePosition && positionResult.screenDimensions) {
          setCurrentPosition(positionResult.interfacePosition)
          setScreenDimensions(positionResult.screenDimensions)
        }
      } else {
        console.error('Failed to position interface:', positionResult?.error)
        setBotStatus((prev) => ({ ...prev, status: 'Failed to position interface' }))
      }

      // Another small delay to allow window positioning to complete
      await new Promise((resolve) => setTimeout(resolve, 300))

      setBotStatus((prev) => ({ ...prev, status: 'Initializing browser...' }))

      const result = await window.api.initializeBot?.()
      if (result?.success) {
        setBotStatus((prev) => ({ ...prev, status: 'Browser initialized' }))

        // Navigate to Google
        await window.api.navigateToUrl?.('https://www.google.com')
        setBotStatus((prev) => ({
          ...prev,
          currentUrl: 'https://www.google.com',
          status: 'Navigated to Google'
        }))
      }
    } catch (error) {
      console.error('Error initializing bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error initializing bot' }))
    }
  }

  const handleStartBot = async (): Promise<void> => {
    try {
      setBotStatus((prev) => ({ ...prev, isRunning: true, status: 'Bot started' }))
      // Use default configuration for minimal UI
      const defaultConfig = {
        accountType: 'Demo' as const,
        number: '1',
        search: 'picture of a cat'
      }
      await window.api.startBot?.(defaultConfig)
    } catch (error) {
      console.error('Error starting bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error starting bot' }))
    }
  }

  const handleStopBot = async (): Promise<void> => {
    try {
      setBotStatus((prev) => ({
        ...prev,
        isRunning: false,
        isPaused: false,
        status: 'Bot stopped'
      }))
      await window.api.stopBot?.()
    } catch (error) {
      console.error('Error stopping bot:', error)
    }
  }

  const handlePauseBot = async (): Promise<void> => {
    try {
      const newPausedState = !botStatus.isPaused
      setBotStatus((prev) => ({
        ...prev,
        isPaused: newPausedState,
        status: newPausedState ? 'Bot paused' : 'Bot resumed'
      }))
      await window.api.pauseBot?.(newPausedState)
    } catch (error) {
      console.error('Error pausing bot:', error)
    }
  }

  const handlePositionChange = async (deltaX: number, deltaY: number): Promise<void> => {
    try {
      const newX = currentPosition.x + deltaX
      const newY = currentPosition.y + deltaY

      const result = await window.api.setWindowPosition?.(newX, newY)
      if (result?.success && result.position) {
        setCurrentPosition(result.position)
        setBotStatus((prev) => ({
          ...prev,
          status: `Position: ${result.position.x}, ${result.position.y}`
        }))
      }
    } catch (error) {
      console.error('Error changing position:', error)
    }
  }

  if (!isStarted) {
    return (
      <div className="bot-interface">
        <div className="bot-welcome">
          <h2>🤖 PocketBot</h2>
          <p>Welcome to PocketBot - Your automated browser assistant</p>
          <button onClick={handleBegin} className="btn-begin">
            Begin
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bot-interface expanded">
      <div className="bot-header">
        <h2>🤖 PocketBot</h2>
        <div className="bot-status">
          <span className={`status-indicator ${botStatus.isRunning ? 'running' : 'stopped'}`}>
            {botStatus.isRunning ? '🟢' : '🔴'}
          </span>
        </div>
      </div>

      <div className="bot-content">
        <div className="bot-controls">
          <div className="control-buttons">
            <button
              onClick={handleStartBot}
              disabled={botStatus.isRunning}
              className="btn-control btn-start btn-primary"
            >
              Start Bot
            </button>
            <button
              onClick={handlePauseBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-pause"
            >
              {botStatus.isPaused ? 'Resume' : 'Pause'}
            </button>
            <button
              onClick={handleStopBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-stop"
            >
              Stop
            </button>
          </div>
        </div>

        <div className="position-controls">
          <h3>Window Position</h3>
          <div className="position-info">
            <span>X: {currentPosition.x}</span>
            <span>Y: {currentPosition.y}</span>
          </div>
          <div className="position-buttons">
            <div className="position-row">
              <button
                onClick={() => handlePositionChange(0, -10)}
                className="btn-position"
                title="Move Up"
              >
                ↑
              </button>
            </div>
            <div className="position-row">
              <button
                onClick={() => handlePositionChange(-10, 0)}
                className="btn-position"
                title="Move Left"
              >
                ←
              </button>
              <button
                onClick={() => handlePositionChange(10, 0)}
                className="btn-position"
                title="Move Right"
              >
                →
              </button>
            </div>
            <div className="position-row">
              <button
                onClick={() => handlePositionChange(0, 10)}
                className="btn-position"
                title="Move Down"
              >
                ↓
              </button>
            </div>
          </div>
          <div className="position-fine-controls">
            <button
              onClick={() => handlePositionChange(-1, 0)}
              className="btn-position-fine"
              title="Fine Left"
            >
              ← 1px
            </button>
            <button
              onClick={() => handlePositionChange(1, 0)}
              className="btn-position-fine"
              title="Fine Right"
            >
              1px →
            </button>
            <button
              onClick={() => handlePositionChange(0, -1)}
              className="btn-position-fine"
              title="Fine Up"
            >
              ↑ 1px
            </button>
            <button
              onClick={() => handlePositionChange(0, 1)}
              className="btn-position-fine"
              title="Fine Down"
            >
              1px ↓
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BotInterface
