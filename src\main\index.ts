import { app, shell, BrowserWindow, ipcMain, screen } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import { autoUpdater } from 'electron-updater'
import BotService from './botService'
import icon from '../../resources/icon.png?asset'

// Configure auto-updater
let mainWindow: BrowserWindow | null = null
let botService: BotService | null = null

// Auto-updater configuration
autoUpdater.checkForUpdatesAndNotify = async () => {
  // Override default behavior to use our custom UI
  return autoUpdater.checkForUpdates()
}

// Auto-updater event handlers
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...')
  mainWindow?.webContents.send('update-checking')
})

autoUpdater.on('update-available', (info) => {
  console.log('Update available:', info)
  mainWindow?.webContents.send('update-available', info)
})

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available:', info)
  mainWindow?.webContents.send('update-not-available', info)
})

autoUpdater.on('error', (err) => {
  console.error('Update error:', err)
  mainWindow?.webContents.send('update-error', err.message)
})

autoUpdater.on('download-progress', (progressObj) => {
  console.log('Download progress:', progressObj)
  mainWindow?.webContents.send('update-download-progress', progressObj)
})

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded:', info)
  mainWindow?.webContents.send('update-downloaded', info)
})

function createWindow(): void {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,
    resizable: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow?.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // Auto-updater IPC handlers
  ipcMain.handle('check-for-updates', async () => {
    if (is.dev) {
      console.log('Auto-updater disabled in development mode - using mock data')

      // Mock update data for testing UI
      const mockUpdate = {
        version: '1.1.0',
        releaseDate: new Date().toISOString(),
        releaseName: 'Test Update',
        releaseNotes:
          '<p>This is a test update for development.</p><ul><li>New feature 1</li><li>Bug fix 2</li><li>Performance improvements</li></ul>'
      }

      // Simulate different scenarios for testing
      const scenarios = [
        { updateAvailable: false, message: 'No updates available (dev mode)' },
        { updateAvailable: true, updateInfo: mockUpdate, message: 'Mock update available' },
        { updateAvailable: false, error: 'Network error (simulated)' }
      ]

      // Cycle through scenarios based on current time (for variety)
      const scenarioIndex = Math.floor(Date.now() / 10000) % scenarios.length
      const result = scenarios[scenarioIndex]

      console.log('Mock update check result:', result)

      // Simulate the update available event for UI testing
      if (result.updateAvailable) {
        setTimeout(() => {
          mainWindow?.webContents.send('update-available', mockUpdate)
        }, 1000)
      }

      return result
    }
    try {
      const result = await autoUpdater.checkForUpdates()
      return { updateAvailable: !!result, updateInfo: result?.updateInfo }
    } catch (error) {
      console.error('Error checking for updates:', error)
      return {
        updateAvailable: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  ipcMain.handle('download-update', async () => {
    if (is.dev) {
      console.log('Simulating update download in development mode')

      // Simulate download progress
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 15
        if (progress > 100) progress = 100

        const mockProgress = {
          bytesPerSecond: 1024 * 1024 * 2.5, // 2.5 MB/s
          percent: progress,
          transferred: (progress / 100) * 50 * 1024 * 1024, // 50MB total
          total: 50 * 1024 * 1024
        }

        mainWindow?.webContents.send('update-download-progress', mockProgress)

        if (progress >= 100) {
          clearInterval(interval)
          setTimeout(() => {
            mainWindow?.webContents.send('update-downloaded', {
              version: '1.1.0',
              releaseDate: new Date().toISOString()
            })
          }, 500)
        }
      }, 200)

      return { success: true, message: 'Mock download started' }
    }
    try {
      await autoUpdater.downloadUpdate()
      return { success: true }
    } catch (error) {
      console.error('Error downloading update:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('install-update', () => {
    if (is.dev) {
      console.log('Simulating update installation in development mode')
      // In development, just show a message instead of actually restarting
      return { success: true, message: 'Mock installation - app would restart in production' }
    }
    try {
      autoUpdater.quitAndInstall()
      return { success: true }
    } catch (error) {
      console.error('Error installing update:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  // Bot IPC handlers
  ipcMain.handle('initialize-bot', async () => {
    try {
      if (!mainWindow) {
        return { success: false, error: 'Main window not available' }
      }

      botService = new BotService(mainWindow)
      const result = await botService.initialize()
      return result
    } catch (error) {
      console.error('Error initializing bot:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('navigate-to-url', async (_, url: string) => {
    try {
      if (!botService) {
        return { success: false, error: 'Bot not initialized' }
      }
      return await botService.navigateToUrl(url)
    } catch (error) {
      console.error('Error navigating to URL:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('query-dom-elements', async () => {
    try {
      if (!botService) {
        return { success: false, error: 'Bot not initialized' }
      }
      return await botService.queryDomElements()
    } catch (error) {
      console.error('Error querying DOM elements:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('start-bot', async (_, config) => {
    try {
      if (!botService) {
        return { success: false, error: 'Bot not initialized' }
      }
      return await botService.startBot(config)
    } catch (error) {
      console.error('Error starting bot:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('stop-bot', async () => {
    try {
      if (!botService) {
        return { success: false, error: 'Bot not initialized' }
      }
      return await botService.stopBot()
    } catch (error) {
      console.error('Error stopping bot:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle('pause-bot', async (_, paused: boolean) => {
    try {
      if (!botService) {
        return { success: false, error: 'Bot not initialized' }
      }
      return await botService.pauseBot(paused)
    } catch (error) {
      console.error('Error pausing bot:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  // Window resize and positioning handler for bot interface
  ipcMain.handle('resize-window', async (_, width: number, height: number) => {
    try {
      console.log('Resizing window to', width, 'x', height)
      if (mainWindow) {
        mainWindow.setSize(width, height)
        mainWindow.center()
        return { success: true }
      }
      return { success: false, error: 'Main window not available' }
    } catch (error) {
      console.error('Error resizing window:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  // Position bot interface at right side and resize bot browser
  ipcMain.handle(
    'position-bot-interface',
    async (_, interfaceWidth: number, interfaceHeight: number) => {
      try {
        if (!mainWindow) {
          return { success: false, error: 'Main window not available' }
        }

        console.log('Positioning bot interface')

        // Get primary display dimensions
        const primaryDisplay = screen.getPrimaryDisplay()
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize

        // Position bot interface at the right side of the screen
        const interfaceX = screenWidth - interfaceWidth
        const interfaceY = Math.floor((screenHeight - interfaceHeight) / 2) // Center vertically

        // Resize and position the bot interface window
        mainWindow.setSize(interfaceWidth, interfaceHeight)
        mainWindow.setPosition(interfaceX, interfaceY)

        // Calculate remaining screen space for bot browser
        const botBrowserWidth = screenWidth - interfaceWidth
        const botBrowserHeight = screenHeight

        console.log('Bot browser dimensions:', botBrowserWidth, 'x', botBrowserHeight)

        // Notify bot service to resize browser if it exists
        if (botService) {
          await botService.resizeBotBrowser(botBrowserWidth, botBrowserHeight)
        }

        return {
          success: true,
          screenDimensions: { screenWidth, screenHeight },
          interfacePosition: { x: interfaceX, y: interfaceY },
          botBrowserDimensions: { width: botBrowserWidth, height: botBrowserHeight }
        }
      } catch (error) {
        console.error('Error positioning bot interface:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  )

  createWindow()

  // Initialize auto-updater (only in production)
  if (!is.dev) {
    // Check for updates on startup
    setTimeout(() => {
      autoUpdater.checkForUpdatesAndNotify()
    }, 3000) // Wait 3 seconds after startup

    // Check for updates every 4 hours
    setInterval(
      () => {
        autoUpdater.checkForUpdatesAndNotify()
      },
      4 * 60 * 60 * 1000
    )
  }

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', async () => {
  // Cleanup bot service before quitting
  if (botService) {
    await botService.cleanup()
    botService = null
  }

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
